Okay, this is an ambitious but very good project! Creating a "fully functional program" that spans frontend, backend, and database in a single response is impossible in terms of actual code. However, I can provide you with a comprehensive architectural design, key code snippets/logic for each part, and a step-by-step guide on how to build it.

You mentioned C++ for the backend. While *possible* (using frameworks like CppCMS, Wt, or Drogon), it's highly unconventional for this type of web application compared to Node.js, Python, or Java, primarily due to the development speed, ecosystem, and readily available libraries for web tasks.
**Given the request for a "fully functional program" and the flexibility offered for backend tech, I will proceed with a more common and efficient stack for web development: Node.js (Express) for the backend and React for the frontend. This will make it easier to achieve a "fully functional" prototype faster.**

If you are *absolutely set* on C++ for the backend, the overall architecture (API endpoints, database interactions) would remain similar, but the implementation details and tooling would be significantly different and more complex. I'll add a note on C++ considerations at the end.

Let's outline the project.

**Project Title: Job Portal System Using MongoDB**

**1. Chosen Technology Stack:**

*   **Frontend:** React (with Vite for speed), Tailwind CSS (for styling), Axios (for API calls)
*   **Backend:** Node.js with Express.js
*   **Database:** MongoDB (with Mongoose ODM for Node.js)
*   **Authentication:** JWT (JSON Web Tokens)
*   **Deployment (Optional):**
    *   MongoDB Atlas (Cloud MongoDB)
    *   Vercel/Netlify (Frontend)
    *   Heroku/Fly.io/Render (Backend)

**2. Directory Structure (Monorepo Approach - simplified):**

```
job-portal/
├── backend/
│   ├── node_modules/
│   ├── config/
│   │   └── db.js         # MongoDB connection
│   │   └── index.js      # Central config (JWT secret, port)
│   ├── controllers/
│   │   ├── authController.js
│   │   ├── jobController.js
│   │   ├── profileController.js
│   │   └── applicationController.js
│   │   └── analyticsController.js
│   ├── middleware/
│   │   ├── authMiddleware.js # JWT verification, role checks
│   │   └── errorMiddleware.js
│   ├── models/
│   │   ├── User.js
│   │   ├── Job.js
│   │   ├── Profile.js      # Candidate profile
│   │   └── Application.js
│   ├── routes/
│   │   ├── authRoutes.js
│   │   ├── jobRoutes.js
│   │   ├── profileRoutes.js
│   │   └── applicationRoutes.js
│   │   └── analyticsRoutes.js
│   ├── server.js           # Main Express app setup
│   └── package.json
│   └── .env                # Environment variables
├── frontend/
│   ├── node_modules/
│   ├── public/
│   ├── src/
│   │   ├── assets/
│   │   ├── components/     # Reusable UI components (Button, Card, Input, Navbar, Modal)
│   │   │   ├── auth/       # Login, Register components
│   │   │   ├── jobs/       # JobCard, JobForm, JobList, JobSearch
│   │   │   ├── profiles/   # ProfileForm, ProfileView
│   │   │   ├── applications/ # ApplicationList, ApplicationCard
│   │   │   └── common/     # Navbar, Footer, Spinner
│   │   ├── contexts/       # React Context for Auth, etc. (e.g., AuthContext.js)
│   │   ├── hooks/          # Custom hooks (e.g., useAuth.js)
│   │   ├── pages/          # Top-level route components (HomePage, LoginPage, DashboardPage, etc.)
│   │   ├── services/       # API call functions (authService.js, jobService.js)
│   │   ├── utils/          # Helper functions
│   │   ├── App.jsx
│   │   ├── main.jsx
│   │   └── index.css       # Tailwind directives
│   ├── package.json
│   ├── vite.config.js
│   └── tailwind.config.js
│   └── .env.local          # Frontend environment variables
└── README.md
```

**3. Database Schema Design (Mongoose Models - `backend/models/`)**

*   **`User.js`**
    ```javascript
    const mongoose = require('mongoose');
    const bcrypt = require('bcryptjs');

    const userSchema = new mongoose.Schema({
        email: { type: String, required: true, unique: true, trim: true, lowercase: true },
        password: { type: String, required: true },
        name: { type: String, required: true },
        role: { type: String, enum: ['candidate', 'employer'], required: true },
        companyName: { type: String, required: function() { return this.role === 'employer'; } },
        createdAt: { type: Date, default: Date.now }
    });

    // Hash password before saving
    userSchema.pre('save', async function(next) {
        if (!this.isModified('password')) return next();
        const salt = await bcrypt.genSalt(10);
        this.password = await bcrypt.hash(this.password, salt);
        next();
    });

    userSchema.methods.matchPassword = async function(enteredPassword) {
        return await bcrypt.compare(enteredPassword, this.password);
    };

    // Index for email for faster login lookups
    userSchema.index({ email: 1 });

    module.exports = mongoose.model('User', userSchema);
    ```

*   **`Job.js`**
    ```javascript
    const mongoose = require('mongoose');

    const jobSchema = new mongoose.Schema({
        employerId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
        title: { type: String, required: true, trim: true },
        companyName: { type: String, required: true }, // Denormalized for easier display, or populate from User
        location: { type: String, required: true, trim: true },
        description: { type: String, required: true },
        requirements: [{ type: String }], // Array of strings
        skills: [{ type: String, trim: true, lowercase: true }], // For easier searching
        salaryMin: { type: Number },
        salaryMax: { type: Number },
        jobType: { type: String, enum: ['Full-time', 'Part-time', 'Contract', 'Internship'] },
        postedDate: { type: Date, default: Date.now },
        applicationDeadline: { type: Date },
        isActive: { type: Boolean, default: true }, // For soft delete
        views: { type: Number, default: 0 },
        applicationsCount: {type: Number, default: 0 },
        createdAt: { type: Date, default: Date.now },
        updatedAt: { type: Date, default: Date.now }
    });

    jobSchema.pre('save', function(next) {
        this.updatedAt = Date.now();
        next();
    });

    // Indexes for searching
    jobSchema.index({ title: 'text', companyName: 'text', location: 'text', description: 'text', skills: 'text' }); // Text index for general search
    jobSchema.index({ employerId: 1 });
    jobSchema.index({ location: 1 });
    jobSchema.index({ skills: 1 });
    jobSchema.index({ jobType: 1 });
    jobSchema.index({ postedDate: -1 });
    jobSchema.index({ isActive: 1, postedDate: -1 }); // For active jobs sorted by date

    module.exports = mongoose.model('Job', jobSchema);
    ```

*   **`Profile.js` (Candidate Profile/Resume)**
    ```javascript
    const mongoose = require('mongoose');

    const experienceSchema = new mongoose.Schema({
        title: { type: String, required: true },
        company: { type: String, required: true },
        location: { type: String },
        startDate: { type: Date, required: true },
        endDate: { type: Date }, // Null if current
        description: { type: String }
    });

    const educationSchema = new mongoose.Schema({
        institution: { type: String, required: true },
        degree: { type: String, required: true },
        fieldOfStudy: { type: String },
        startDate: { type: Date },
        endDate: { type: Date }, // Or graduation year
        grade: { type: String }
    });

    const profileSchema = new mongoose.Schema({
        candidateId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, unique: true },
        headline: { type: String, trim: true }, // e.g., "Full Stack Developer | MERN | AWS"
        summary: { type: String },
        skills: [{ type: String, trim: true, lowercase: true }],
        experience: [experienceSchema],
        education: [educationSchema],
        resumeUrl: { type: String }, // URL to S3/Cloudinary stored resume PDF (optional)
        contact: {
            phone: { type: String },
            linkedin: { type: String },
            github: { type: String },
            portfolio: { type: String }
        },
        isVisible: { type: Boolean, default: true }, // For candidate to hide profile
        createdAt: { type: Date, default: Date.now },
        updatedAt: { type: Date, default: Date.now }
    });

    profileSchema.pre('save', function(next) {
        this.updatedAt = Date.now();
        next();
    });
    
    profileSchema.index({ candidateId: 1 });
    profileSchema.index({ skills: 1 }); // Crucial for recruiter search
    profileSchema.index({ 'experience.title': 'text', 'experience.company': 'text', summary: 'text', skills: 'text' }); // Text index

    module.exports = mongoose.model('Profile', profileSchema);
    ```

*   **`Application.js`**
    ```javascript
    const mongoose = require('mongoose');

    const applicationSchema = new mongoose.Schema({
        jobId: { type: mongoose.Schema.Types.ObjectId, ref: 'Job', required: true },
        candidateId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
        employerId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, // Denormalized for quick employer filtering
        status: { 
            type: String, 
            enum: ['Applied', 'Viewed', 'Shortlisted', 'Interviewing', 'Offered', 'Rejected', 'Withdrawn'], 
            default: 'Applied' 
        },
        applicationDate: { type: Date, default: Date.now },
        coverLetter: { type: String },
        // Snapshot of key profile info at time of application (optional, for historical record)
        profileSnapshot: { 
            name: String,
            email: String,
            skills: [String],
            headline: String
        },
        updatedAt: { type: Date, default: Date.now }
    });

    applicationSchema.pre('save', function(next) {
        this.updatedAt = Date.now();
        next();
    });

    applicationSchema.index({ jobId: 1, candidateId: 1 }, { unique: true }); // Candidate can apply once per job
    applicationSchema.index({ jobId: 1 });
    applicationSchema.index({ candidateId: 1 });
    applicationSchema.index({ employerId: 1 });
    applicationSchema.index({ status: 1 });

    module.exports = mongoose.model('Application', applicationSchema);
    ```

**4. Backend Implementation (Node.js/Express.js - Key Parts)**

*   **`backend/server.js`**
    ```javascript
    const express = require('express');
    const dotenv = require('dotenv');
    const cors = require('cors');
    const connectDB = require('./config/db');
    const { notFound, errorHandler } = require('./middleware/errorMiddleware');

    // Import routes
    const authRoutes = require('./routes/authRoutes');
    const jobRoutes = require('./routes/jobRoutes');
    const profileRoutes = require('./routes/profileRoutes');
    const applicationRoutes = require('./routes/applicationRoutes');
    const analyticsRoutes = require('./routes/analyticsRoutes');

    dotenv.config();
    connectDB();

    const app = express();
    app.use(cors()); // Configure appropriately for production
    app.use(express.json()); // To parse JSON bodies

    // API Routes
    app.use('/api/auth', authRoutes);
    app.use('/api/jobs', jobRoutes);
    app.use('/api/profiles', profileRoutes);
    app.use('/api/applications', applicationRoutes);
    app.use('/api/analytics', analyticsRoutes);

    app.get('/', (req, res) => res.send('Job Portal API Running'));

    // Error Handling Middleware
    app.use(notFound);
    app.use(errorHandler);

    const PORT = process.env.PORT || 5000;
    app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
    ```

*   **`backend/config/db.js`**
    ```javascript
    const mongoose = require('mongoose');

    const connectDB = async () => {
        try {
            const conn = await mongoose.connect(process.env.MONGO_URI, {
                useNewUrlParser: true,
                useUnifiedTopology: true,
                // useCreateIndex: true, // No longer needed in Mongoose 6+
                // useFindAndModify: false // No longer needed in Mongoose 6+
            });
            console.log(`MongoDB Connected: ${conn.connection.host}`);
        } catch (error) {
            console.error(`Error connecting to MongoDB: ${error.message}`);
            process.exit(1);
        }
    };
    module.exports = connectDB;
    ```

*   **`backend/middleware/authMiddleware.js`**
    ```javascript
    const jwt = require('jsonwebtoken');
    const User = require('../models/User');

    const protect = async (req, res, next) => {
        let token;
        if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
            try {
                token = req.headers.authorization.split(' ')[1];
                const decoded = jwt.verify(token, process.env.JWT_SECRET);
                req.user = await User.findById(decoded.id).select('-password');
                if (!req.user) {
                    return res.status(401).json({ message: 'Not authorized, user not found' });
                }
                next();
            } catch (error) {
                console.error(error);
                res.status(401).json({ message: 'Not authorized, token failed' });
            }
        }
        if (!token) {
            res.status(401).json({ message: 'Not authorized, no token' });
        }
    };

    const employer = (req, res, next) => {
        if (req.user && req.user.role === 'employer') {
            next();
        } else {
            res.status(403).json({ message: 'Not authorized as an employer' });
        }
    };

    const candidate = (req, res, next) => {
        if (req.user && req.user.role === 'candidate') {
            next();
        } else {
            res.status(403).json({ message: 'Not authorized as a candidate' });
        }
    };

    module.exports = { protect, employer, candidate };
    ```

*   **Example Controller: `backend/controllers/jobController.js`**
    ```javascript
    const Job = require('../models/Job');
    const Application = require('../models/Application'); // For updating application counts

    // @desc    Create a new job listing
    // @route   POST /api/jobs
    // @access  Private/Employer
    const createJob = async (req, res) => {
        try {
            const { title, description, location, requirements, skills, salaryMin, salaryMax, jobType, applicationDeadline } = req.body;
            
            const job = new Job({
                employerId: req.user._id,
                companyName: req.user.companyName, // Assuming companyName is on User model for employer
                title,
                description,
                location,
                requirements: requirements ? requirements.split(',').map(r => r.trim()) : [],
                skills: skills ? skills.split(',').map(s => s.trim().toLowerCase()) : [],
                salaryMin,
                salaryMax,
                jobType,
                applicationDeadline
            });

            const createdJob = await job.save();
            res.status(201).json(createdJob);
        } catch (error) {
            console.error(error.message);
            if (error.name === 'ValidationError') {
                return res.status(400).json({ message: "Validation Error", errors: error.errors });
            }
            res.status(500).json({ message: "Server Error creating job" });
        }
    };

    // @desc    Get all active jobs with search, filter, pagination, sort
    // @route   GET /api/jobs
    // @access  Public
    const getJobs = async (req, res) => {
        const pageSize = parseInt(req.query.pageSize) || 10;
        const page = parseInt(req.query.page) || 1;
        
        let query = { isActive: true };
        
        // Text search
        if (req.query.keyword) {
            query.$text = { $search: req.query.keyword };
        }
        
        // Specific field filters
        if (req.query.location) {
            query.location = { $regex: req.query.location, $options: 'i' };
        }
        if (req.query.company) {
            query.companyName = { $regex: req.query.company, $options: 'i' };
        }
        if (req.query.skills) {
            const skillsArray = req.query.skills.split(',').map(s => s.trim().toLowerCase());
            query.skills = { $all: skillsArray }; // Must have all specified skills
        }
        if (req.query.jobType) {
            query.jobType = req.query.jobType;
        }
        if (req.query.salaryMin) {
            query.salaryMin = { $gte: parseInt(req.query.salaryMin) };
        }
         if (req.query.salaryMax) {
            query.salaryMax = { $lte: parseInt(req.query.salaryMax) };
        }

        let sortOptions = { postedDate: -1 }; // Default sort: newest first
        if (req.query.sortBy) {
            const parts = req.query.sortBy.split(':'); // e.g., title:asc or salaryMin:desc
            sortOptions = {};
            sortOptions[parts[0]] = parts[1] === 'desc' ? -1 : 1;
        }

        try {
            // To demonstrate .explain(), you'd typically run this in mongo shell or use mongoose's .explain()
            // const explainResult = await Job.find(query).explain("executionStats");
            // console.log("Query explanation:", explainResult);

            const count = await Job.countDocuments(query);
            const jobs = await Job.find(query)
                .populate('employerId', 'name companyName') // Populate employer details
                .sort(sortOptions)
                .limit(pageSize)
                .skip(pageSize * (page - 1));
            
            res.json({ 
                jobs, 
                page, 
                pages: Math.ceil(count / pageSize),
                totalJobs: count 
            });
        } catch (error) {
            console.error(error.message);
            res.status(500).json({ message: "Server Error fetching jobs" });
        }
    };

    // @desc    Get job by ID
    // @route   GET /api/jobs/:id
    // @access  Public
    const getJobById = async (req, res) => {
        try {
            const job = await Job.findById(req.params.id).populate('employerId', 'name companyName email');
            if (job && job.isActive) {
                // Optionally increment views if desired (beware of race conditions if not careful)
                // job.views = (job.views || 0) + 1; 
                // await job.save();
                res.json(job);
            } else {
                res.status(404).json({ message: 'Job not found or inactive' });
            }
        } catch (error) {
            console.error(error.message);
            if (error.kind === 'ObjectId') {
                return res.status(404).json({ message: 'Job not found' });
            }
            res.status(500).json({ message: "Server Error fetching job" });
        }
    };

    // @desc    Update a job listing
    // @route   PUT /api/jobs/:id
    // @access  Private/Employer (owner)
    const updateJob = async (req, res) => {
        try {
            const job = await Job.findById(req.params.id);

            if (!job) {
                return res.status(404).json({ message: 'Job not found' });
            }
            if (job.employerId.toString() !== req.user._id.toString()) {
                return res.status(403).json({ message: 'User not authorized to update this job' });
            }

            const { title, description, location, requirements, skills, salaryMin, salaryMax, jobType, applicationDeadline, isActive } = req.body;

            job.title = title || job.title;
            job.description = description || job.description;
            job.location = location || job.location;
            if (requirements) job.requirements = requirements.split(',').map(r => r.trim());
            if (skills) job.skills = skills.split(',').map(s => s.trim().toLowerCase());
            // Use $set for specific fields if you want to be more precise and allow nullifying fields
            if (salaryMin !== undefined) job.salaryMin = salaryMin;
            if (salaryMax !== undefined) job.salaryMax = salaryMax;
            job.jobType = jobType || job.jobType;
            job.applicationDeadline = applicationDeadline || job.applicationDeadline;
            if (isActive !== undefined) job.isActive = isActive; // Allows closing/reopening listing
            job.updatedAt = Date.now();

            const updatedJob = await job.save();
            res.json(updatedJob);
        } catch (error) {
            console.error(error.message);
             if (error.name === 'ValidationError') {
                return res.status(400).json({ message: "Validation Error", errors: error.errors });
            }
            res.status(500).json({ message: "Server Error updating job" });
        }
    };

    // @desc    Delete a job listing (soft delete)
    // @route   DELETE /api/jobs/:id
    // @access  Private/Employer (owner)
    const deleteJob = async (req, res) => {
        try {
            const job = await Job.findById(req.params.id);

            if (!job) {
                return res.status(404).json({ message: 'Job not found' });
            }
            if (job.employerId.toString() !== req.user._id.toString()) {
                return res.status(403).json({ message: 'User not authorized to delete this job' });
            }

            job.isActive = false; // Soft delete
            job.updatedAt = Date.now();
            await job.save();
            // Could also do: await Job.updateOne({ _id: req.params.id, employerId: req.user._id }, { $set: { isActive: false, updatedAt: Date.now() } });

            res.json({ message: 'Job listing deactivated (soft deleted)' });
            // For hard delete: await job.remove(); or await Job.deleteOne({ _id: req.params.id, employerId: req.user._id });
        } catch (error) {
            console.error(error.message);
            res.status(500).json({ message: "Server Error deleting job" });
        }
    };
    
    module.exports = { createJob, getJobs, getJobById, updateJob, deleteJob };
    ```
    *   **Note on `.explain()`:** To analyze query performance, you'd typically use the MongoDB shell or a GUI like Compass. `Job.find(query).explain('executionStats')` can be used in Mongoose, and you'd log the output. For example, before adding an index, `explain()` might show a `COLLSCAN` (collection scan). After adding a proper index, it should show an `IXSCAN` (index scan), which is much more performant.

*   **`backend/controllers/profileController.js` (Candidate Profiles)**
    *   `createOrUpdateProfile`: Allows candidates to create or update their profile. Uses `findOneAndUpdate` with `upsert: true`.
    *   `getMyProfile`: Gets the logged-in candidate's profile.
    *   `getProfileByCandidateId`: For employers to view a specific candidate's profile.
    *   `searchProfiles`: For employers to search candidates by skills, experience keywords, etc. Similar logic to `getJobs` search.

*   **`backend/controllers/applicationController.js`**
    *   `applyToJob`: Candidate applies. Creates an Application document. Links Job, Candidate, Employer. Increments `applicationsCount` on the `Job` model using `$inc`.
    *   `getMyApplications`: Candidate views their applications.
    *   `getJobApplications`: Employer views applications for a specific job they posted.
    *   `updateApplicationStatus`: Employer updates the status of an application.

*   **`backend/controllers/analyticsController.js` (Aggregation Examples)**
    ```javascript
    const Job = require('../models/Job');
    const Profile = require('../models/Profile');
    const Application = require('../models/Application');

    // @desc    Number of jobs posted per industry (assuming 'industry' field exists or derived)
    // For this example, let's use jobType as a proxy if 'industry' is not a direct field.
    // @route   GET /api/analytics/jobs-per-category
    // @access  Public (or Admin/Employer)
    const getJobsPerCategory = async (req, res) => {
        try {
            // If you add an 'industry' field to Job model, use 'industry' instead of 'jobType'
            const stats = await Job.aggregate([
                { $match: { isActive: true } }, // Only active jobs
                { $group: { _id: "$jobType", count: { $sum: 1 } } },
                { $sort: { count: -1 } },
                { $project: { category: "$_id", count: 1, _id: 0 } }
            ]);
            res.json(stats);
        } catch (error) {
            res.status(500).json({ message: "Error fetching job category stats", error: error.message });
        }
    };

    // @desc    Most in-demand skills (from job postings)
    // @route   GET /api/analytics/in-demand-skills
    // @access  Public
    const getInDemandSkills = async (req, res) => {
        try {
            const stats = await Job.aggregate([
                { $match: { isActive: true } },
                { $unwind: "$skills" }, // Deconstructs the skills array
                { $group: { _id: "$skills", count: { $sum: 1 } } },
                { $sort: { count: -1 } },
                { $limit: 10 }, // Top 10 skills
                { $project: { skill: "$_id", count: 1, _id: 0 } }
            ]);
            res.json(stats);
        } catch (error) {
            res.status(500).json({ message: "Error fetching in-demand skills", error: error.message });
        }
    };
    
    // @desc    Average salary range by job role (title)
    // @route   GET /api/analytics/avg-salary-by-role
    // @access  Public
    const getAvgSalaryByRole = async (req, res) => {
        try {
            const stats = await Job.aggregate([
                { $match: { isActive: true, salaryMin: { $ne: null }, salaryMax: { $ne: null } } },
                { 
                    $group: { 
                        _id: "$title", // Group by job title
                        avgMinSalary: { $avg: "$salaryMin" },
                        avgMaxSalary: { $avg: "$salaryMax" },
                        count: { $sum: 1 }
                    } 
                },
                { $match: { count: { $gte: 3 } } }, // Only show roles with at least 3 listings for meaningful average
                { $sort: { count: -1 } },
                { $limit: 10 },
                { 
                    $project: { 
                        role: "$_id", 
                        averageMinSalary: { $round: ["$avgMinSalary", 0] }, 
                        averageMaxSalary: { $round: ["$avgMaxSalary", 0] },
                        jobPostings: "$count",
                        _id: 0 
                    } 
                }
            ]);
            res.json(stats);
        } catch (error) {
            res.status(500).json({ message: "Error fetching average salary stats", error: error.message });
        }
    };

    // @desc    Application trends over time (e.g., daily applications in last 30 days)
    // @route   GET /api/analytics/application-trends
    // @access  Private/Employer or Admin
    const getApplicationTrends = async (req, res) => {
        try {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const stats = await Application.aggregate([
                { $match: { applicationDate: { $gte: thirtyDaysAgo } } }, // Filter for last 30 days
                {
                    $group: {
                        _id: { $dateToString: { format: "%Y-%m-%d", date: "$applicationDate" } },
                        count: { $sum: 1 }
                    }
                },
                { $sort: { _id: 1 } }, // Sort by date
                { $project: { date: "$_id", applications: "$count", _id: 0 } }
            ]);
            res.json(stats);
        } catch (error) {
            res.status(500).json({ message: "Error fetching application trends", error: error.message });
        }
    };

    module.exports = { getJobsPerCategory, getInDemandSkills, getAvgSalaryByRole, getApplicationTrends };
    ```

*   **Routes (`backend/routes/`)** would link paths to controller functions, applying middleware. E.g., `backend/routes/jobRoutes.js`:
    ```javascript
    const express = require('express');
    const router = express.Router();
    const { createJob, getJobs, getJobById, updateJob, deleteJob } = require('../controllers/jobController');
    const { protect, employer } = require('../middleware/authMiddleware');

    router.route('/')
        .post(protect, employer, createJob) // Only employers can create
        .get(getJobs); // Public can search

    router.route('/:id')
        .get(getJobById) // Public can view
        .put(protect, employer, updateJob) // Only employer owner can update
        .delete(protect, employer, deleteJob); // Only employer owner can delete

    module.exports = router;
    ```

**5. Frontend Implementation (React - Key Parts)**

*   **`frontend/src/services/api.js` (or individual service files like `jobService.js`)**
    ```javascript
    import axios from 'axios';

    const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api'; // Set in .env.local

    const apiClient = axios.create({
        baseURL: API_URL,
        headers: {
            'Content-Type': 'application/json',
        },
    });

    // Interceptor to add JWT token to requests
    apiClient.interceptors.request.use(config => {
        const user = JSON.parse(localStorage.getItem('userInfo')); // Or get from context
        if (user && user.token) {
            config.headers.Authorization = `Bearer ${user.token}`;
        }
        return config;
    }, error => {
        return Promise.reject(error);
    });

    // Job Service Example
    export const jobService = {
        getJobs: (params) => apiClient.get('/jobs', { params }), // params for search, filter, page
        getJobById: (id) => apiClient.get(`/jobs/${id}`),
        createJob: (jobData) => apiClient.post('/jobs', jobData),
        updateJob: (id, jobData) => apiClient.put(`/jobs/${id}`, jobData),
        deleteJob: (id) => apiClient.delete(`/jobs/${id}`),
    };

    // Auth Service Example
    export const authService = {
        login: (credentials) => apiClient.post('/auth/login', credentials),
        register: (userData) => apiClient.post('/auth/register', userData),
    };

    // Profile Service Example
    export const profileService = {
        getMyProfile: () => apiClient.get('/profiles/me'),
        updateProfile: (profileData) => apiClient.put('/profiles/me', profileData),
        // ... other profile methods
    };
    
    // Application Service Example
    export const applicationService = {
        apply: (applicationData) => apiClient.post('/applications', applicationData),
        getMyApplications: () => apiClient.get('/applications/my'),
        // ... other application methods for employers
    };

    // Analytics Service Example
    export const analyticsService = {
        getJobsPerCategory: () => apiClient.get('/analytics/jobs-per-category'),
        getInDemandSkills: () => apiClient.get('/analytics/in-demand-skills'),
        // ... other analytics methods
    };
    ```

*   **`frontend/src/contexts/AuthContext.js`** (for managing user authentication state)
    ```jsx
    import React, { createContext, useState, useEffect, useContext } from 'react';
    import { authService } from '../services/api'; // Adjust path as needed

    const AuthContext = createContext(null);

    export const AuthProvider = ({ children }) => {
        const [user, setUser] = useState(null);
        const [loading, setLoading] = useState(true);

        useEffect(() => {
            const userInfo = localStorage.getItem('userInfo');
            if (userInfo) {
                setUser(JSON.parse(userInfo));
            }
            setLoading(false);
        }, []);

        const login = async (email, password) => {
            try {
                const { data } = await authService.login({ email, password });
                localStorage.setItem('userInfo', JSON.stringify(data));
                setUser(data);
                return data;
            } catch (error) {
                console.error("Login failed:", error.response?.data?.message || error.message);
                throw error;
            }
        };

        const register = async (userData) => {
            try {
                const { data } = await authService.register(userData);
                localStorage.setItem('userInfo', JSON.stringify(data));
                setUser(data);
                return data;
            } catch (error) {
                console.error("Registration failed:", error.response?.data?.message || error.message);
                throw error;
            }
        };

        const logout = () => {
            localStorage.removeItem('userInfo');
            setUser(null);
        };

        return (
            <AuthContext.Provider value={{ user, setUser, login, register, logout, loading }}>
                {!loading && children}
            </AuthContext.Provider>
        );
    };

    export const useAuth = () => useContext(AuthContext);
    ```
    Wrap your `<App />` component with `<AuthProvider />` in `main.jsx`.

*   **Example Component: `frontend/src/pages/JobsPage.jsx`**
    ```jsx
    import React, { useState, useEffect } from 'react';
    import { jobService } from '../services/api';
    import JobCard from '../components/jobs/JobCard'; // You'll create this
    import JobSearchForm from '../components/jobs/JobSearchForm'; // You'll create this
    import Pagination from '../components/common/Pagination'; // You'll create this

    function JobsPage() {
        const [jobs, setJobs] = useState([]);
        const [loading, setLoading] = useState(true);
        const [error, setError] = useState('');
        const [currentPage, setCurrentPage] = useState(1);
        const [totalPages, setTotalPages] = useState(1);
        const [totalJobs, setTotalJobs] = useState(0);
        const [searchParams, setSearchParams] = useState({});

        useEffect(() => {
            const fetchJobs = async () => {
                setLoading(true);
                setError('');
                try {
                    const params = { ...searchParams, page: currentPage, pageSize: 10 };
                    const { data } = await jobService.getJobs(params);
                    setJobs(data.jobs);
                    setTotalPages(data.pages);
                    setTotalJobs(data.totalJobs);
                } catch (err) {
                    setError(err.response?.data?.message || 'Failed to fetch jobs.');
                    console.error(err);
                }
                setLoading(false);
            };
            fetchJobs();
        }, [currentPage, searchParams]);

        const handleSearch = (newSearchParams) => {
            setSearchParams(newSearchParams);
            setCurrentPage(1); // Reset to first page on new search
        };

        const handlePageChange = (page) => {
            setCurrentPage(page);
        };

        if (loading) return <div className="text-center p-8">Loading jobs...</div>;
        if (error) return <div className="text-center p-8 text-red-500">{error}</div>;

        return (
            <div className="container mx-auto p-4">
                <h1 className="text-3xl font-bold mb-6 text-center">Find Your Next Opportunity</h1>
                <JobSearchForm onSearch={handleSearch} />
                
                <div className="my-4 text-gray-600">
                    Showing {jobs.length} of {totalJobs} jobs
                </div>

                {jobs.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {jobs.map(job => (
                            <JobCard key={job._id} job={job} />
                        ))}
                    </div>
                ) : (
                    <p className="text-center text-gray-500 mt-8">No jobs found matching your criteria.</p>
                )}

                {totalPages > 1 && (
                    <Pagination 
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={handlePageChange}
                    />
                )}
            </div>
        );
    }

    export default JobsPage;
    ```

*   **`frontend/src/components/jobs/JobCard.jsx` (Simplified)**
    ```jsx
    import React from 'react';
    import { Link } from 'react-router-dom'; // Assuming you use React Router

    function JobCard({ job }) {
        return (
            <div className="bg-white shadow-lg rounded-lg p-6 hover:shadow-xl transition-shadow duration-300">
                <h2 className="text-xl font-semibold text-blue-600 mb-2">{job.title}</h2>
                <p className="text-gray-700 mb-1">{job.companyName}</p>
                <p className="text-gray-500 mb-3">{job.location}</p>
                <div className="mb-3">
                    {job.skills.slice(0, 3).map(skill => (
                        <span key={skill} className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full mr-1 mb-1 inline-block">{skill}</span>
                    ))}
                    {job.skills.length > 3 && <span className="text-xs text-gray-500"> +{job.skills.length - 3} more</span>}
                </div>
                <p className="text-sm text-gray-600 mb-4 truncate">
                    {job.description.substring(0,100)}...
                </p>
                <Link 
                    to={`/jobs/${job._id}`} 
                    className="text-blue-500 hover:text-blue-700 font-medium"
                >
                    View Details &rarr;
                </Link>
            </div>
        );
    }
    export default JobCard;
    ```

*   **Analytics Frontend:** Use a charting library like `Chart.js` (with `react-chartjs-2`), `Recharts`, or `Nivo`. Fetch data from your analytics API endpoints and render charts.

**6. Data Validation & Error Handling:**

*   **Backend:**
    *   Mongoose schema validation (e.g., `required`, `enum`, `min`, `max`, custom validators).
    *   Express middleware for validating request bodies (e.g., using `express-validator`).
    *   Centralized error handling middleware (`backend/middleware/errorMiddleware.js`) to catch errors and send consistent JSON responses.
*   **Frontend:**
    *   Form validation (e.g., HTML5 attributes, libraries like `Formik` with `Yup`).
    *   Display user-friendly error messages from API responses.
    *   Handle network errors gracefully.

**7. Setup and Running:**

*   **MongoDB:**
    *   Install MongoDB locally or use MongoDB Atlas (cloud).
    *   Get your `MONGO_URI` connection string.
*   **Backend:**
    1.  `cd backend`
    2.  Create `.env` file:
        ```
        PORT=5000
        MONGO_URI=your_mongodb_connection_string
        JWT_SECRET=your_very_strong_jwt_secret
        NODE_ENV=development
        ```
    3.  `npm install`
    4.  `npm start` (or `npm run dev` if you set up nodemon)
*   **Frontend:**
    1.  `cd frontend`
    2.  Create `.env.local` file (for Vite, or `.env` for Create React App):
        ```
        VITE_REACT_APP_API_URL=http://localhost:5000/api 
        ```
        (Note: Vite requires env vars to be prefixed with `VITE_`)
    3.  `npm install`
    4.  `npm run dev` (for Vite) or `npm start` (for CRA)

**8. Core Modules Implementation Summary:**

*   **Insert New Job Post / Resume:**
    *   Frontend forms, API calls to `POST /api/jobs` or `POST/PUT /api/profiles/me`.
    *   Backend controllers handle saving to MongoDB.
*   **Search Jobs / Candidates:**
    *   Frontend search forms, API calls to `GET /api/jobs` or `GET /api/profiles` with query parameters.
    *   Backend controllers use MongoDB query operators (`$regex`, `$text`, `$or`, `$in`, numeric comparisons for salary) and handle pagination/sorting.
*   **Update Job Post / Profile:**
    *   Frontend forms pre-filled with existing data, API calls to `PUT /api/jobs/:id` or `PUT /api/profiles/me`.
    *   Backend controllers use `$set` (implicitly by Mongoose setters or explicitly) and `$push`/`$pull` for array fields if needed.
*   **Delete Job Post / Resume:**
    *   Frontend confirmation prompt, API call to `DELETE /api/jobs/:id` or `DELETE /api/profiles/me`.
    *   Backend: Soft delete for jobs (`isActive: false`). For profiles, consider if it's a soft delete or hard delete.
*   **Optimize Search:**
    *   Define appropriate indexes in Mongoose schemas (compound, text).
    *   Use MongoDB Compass or `explain()` in mongo shell/Mongoose to analyze query performance and identify bottlenecks.
*   **Aggregation and Analytics:**
    *   Backend controllers with MongoDB Aggregation Framework pipelines.
    *   Frontend fetches analytics data and uses charting libraries to display it.
*   **Authentication Module:**
    *   Frontend login/register forms, `AuthContext` for state.
    *   Backend `authController` for user registration (hashing passwords) and login (issuing JWTs).
    *   `authMiddleware` to protect routes and implement role-based access.
*   **Application Tracking:**
    *   Frontend "Apply" button on job details, form for cover letter.
    *   Backend `applicationController` to create applications, linking Job and Candidate.
    *   Frontend views for candidates to see their applications and employers to see applicants for their jobs.

**9. C++ Backend Considerations (If you MUST use it):**

*   **Frameworks:** You'd need a C++ web framework like:
    *   **Drogon:** Modern, high-performance, uses C++17/20. Asynchronous. Good choice if you want modern C++.
    *   **CppCMS:** Mature, feature-rich, but perhaps less "modern" feeling.
    *   **Wt (Witty):** Widget-based, can feel more like desktop GUI development for the web.
*   **MongoDB Driver:** You'll need a C++ MongoDB driver (e.g., `mongo-cxx-driver`).
*   **JSON Handling:** Libraries like `nlohmann/json` or `RapidJSON`.
*   **Build System:** CMake is standard.
*   **HTTP Server:** The framework usually handles this, or you might integrate with something like Nginx as a reverse proxy.
*   **Development Complexity:** Significantly higher learning curve and slower development pace for typical web features (auth, sessions, ORM-like behavior) compared to Node.js/Python/Java. Less community support specifically for web dev tasks.
*   **API Design:** The REST API endpoints and MongoDB interactions would conceptually be the same, but the C++ code to implement them would be very different.

**Recommendation:**
Stick with Node.js/Express for the backend for this project unless there's an overwhelming academic requirement or personal learning goal to use C++ for the web backend. The provided plan is tailored for the Node.js/React stack, which will be much quicker to get "fully functional."

This detailed plan should give you a solid foundation to start building your Job Portal System. Remember to build incrementally, testing each module as you go. Good luck!



please help me complete this project, 
the project description was 


�
 Project Title: Job Portal System Using MongoDB 
�
�
 Problem Statement: 
Modern job portals must manage a large volume of diverse data: job listings, company details, 
candidate profiles, resumes, applications, and communications. This data is often 
semi-structured—resumes vary in format, job requirements differ by role, and application 
workflows are dynamic—making traditional relational databases inflexible and harder to scale. 
This project aims to design and implement a Job Portal System using MongoDB as the core 
database. As a document-oriented NoSQL database, MongoDB is ideal for handling 
unstructured and variable data, enabling quick searches, flexible schemas, and scalable 
performance. 
The system should support the core operations of posting and managing job listings, registering 
candidates and employers, searching for jobs or candidates, and tracking applications. Students 
are free to choose any frontend (e.g., React, Angular, Vue) and backend (e.g., Node.js, Python, 
Java) technologies, but MongoDB must be used for the database layer. 
�
�
 Objectives: 
● Demonstrate efficient use of MongoDB for modeling real-world job and resume data. 
● Allow flexibility in technology stack for frontend and backend components. 
● Provide experience with advanced querying, indexing, and aggregation in MongoDB. 
● Build a functional system for matching candidates with job opportunities using NoSQL 
data strategies. 
�
�
 Core Modules: 
Insert New Job Post / Resume 
● Allow employers to create job listings with fields like job title, company, location, 
description, requirements, and salary. 
● Allow job seekers to create profiles with resume details, skills, education, and work 
history. 
● Store job posts in a jobs collection and resumes in a candidates or resumes 
collection. 
Search Jobs / Candidates 
● Enable users to search job posts by title, location, skills, salary range, or company. 
● Enable recruiters to search candidate profiles by skills, experience, or keywords. 
● Use advanced MongoDB queries with $regex, $text, $or, and filters. 
● Support pagination and sorting for large result sets. 
Update Job Post / Profile 
● Employers can edit job descriptions, update application deadlines, or close listings. 
● Candidates can update their resumes, add new skills, or modify experience. 
● Use MongoDB’s $set and $push operators for efficient partial updates. 
Delete Job Post / Resume 
● Allow users to delete job listings or candidate profiles securely. 
● Ensure confirmation prompts and logical soft deletes if needed. 
Optimize Search 
● Use compound indexes and text indexes on frequently searched fields like 
jobTitle, skills, and location. 
● Analyze query execution using .explain() and optimize for performance. 
Aggregation and Analytics 
● Use MongoDB Aggregation Framework to generate insights such as: 
○ Number of jobs posted per industry 
○ Most in-demand skills 
○ Average salary range by job role 
○ Application trends over time 
● Display analytics using charts or graphs in the frontend. 
Authentication Module (Optional) 
● Separate login for Employers and Candidates. 
● Use JWT or session-based authentication. 
● Role-based access for managing posts or applying to jobs. 
Application Tracking 
● Allow candidates to apply for jobs. 
● Enable employers to view, filter, and track applications. 
● Store application data in a dedicated applications collection linking job and 
candidate IDs. 
Data Validation & Error Handling 
● Validate entries like email, phone number, required fields, and resume formats. 
● Handle errors gracefully and provide clear feedback to users. 
�
�
 Suggested Technology Stack (Flexible): 
● Frontend: React, Vue.js, Angular, Tailwind, Bootstrap 
● Backend: Node.js (Express), Python (Flask/Django), Java (Spring Boot) 
● Database: MongoDB (Required) 






one addition i have already build a similar project named skillsync but it's using postgresql, i want to take this as context and integrate some modules like posting assignment in the jobportal




You're looking to bring the detailed "Job Portal System Using MongoDB" project from the ai_studio.ipynb notebook, including the new "Assignment Posting" module, to life! That's an ambitious and exciting goal.

As the notebook itself mentions, creating a fully functional program spanning frontend, backend, and database, with all its files and setup, in a single exchange is impractical. However, I can absolutely help you build it piece by piece by providing the complete code for each file as outlined in the architectural design. You would then create the specified directory structure, save these files, install dependencies, and run the project.

Let's start with the backend and lay down the foundation with the MongoDB models.

Here is the code for the model files that should go into your job-portal/backend/models/ directory:

1. User.js
(Based on the notebook, defines user schema for candidates and employers)

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
    email: { type: String, required: true, unique: true, trim: true, lowercase: true },
    password: { type: String, required: true },
    name: { type: String, required: true, trim: true },
    role: { type: String, enum: ['candidate', 'employer'], required: true },
    companyName: { type: String, trim: true, required: function() { return this.role === 'employer'; } },
    createdAt: { type: Date, default: Date.now }
});

// Hash password before saving
userSchema.pre('save', async function(next) {
    if (!this.isModified('password')) return next();
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
});

userSchema.methods.matchPassword = async function(enteredPassword) {
    return await bcrypt.compare(enteredPassword, this.password);
};

// Index for email for faster login lookups
userSchema.index({ email: 1 });

module.exports = mongoose.model('User', userSchema);


2. Job.js
(Based on the notebook, with the addition of hasAssignment flag for the new module)

const mongoose = require('mongoose');

const jobSchema = new mongoose.Schema({
    employerId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    title: { type: String, required: true, trim: true },
    companyName: { type: String, required: true, trim: true }, // Denormalized for easier display, or populate from User
    location: { type: String, required: true, trim: true },
    description: { type: String, required: true },
    requirements: [{ type: String }], // Array of strings
    skills: [{ type: String, trim: true, lowercase: true }], // For easier searching
    salaryMin: { type: Number },
    salaryMax: { type: Number },
    jobType: { type: String, enum: ['Full-time', 'Part-time', 'Contract', 'Internship'] },
    postedDate: { type: Date, default: Date.now },
    applicationDeadline: { type: Date },
    isActive: { type: Boolean, default: true }, // For soft delete
    views: { type: Number, default: 0 },
    applicationsCount: {type: Number, default: 0 },
    hasAssignment: { type: Boolean, default: false }, // Indicates if a job has an associated assignment
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});

jobSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// Indexes for searching
jobSchema.index({ title: 'text', companyName: 'text', location: 'text', description: 'text', skills: 'text' }); // Text index for general search
jobSchema.index({ employerId: 1 });
jobSchema.index({ location: 1 });
jobSchema.index({ skills: 1 });
jobSchema.index({ jobType: 1 });
jobSchema.index({ postedDate: -1 });
jobSchema.index({ isActive: 1, postedDate: -1 }); // For active jobs sorted by date

module.exports = mongoose.model('Job', jobSchema);
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

3. Profile.js
(Based on the notebook, for candidate profiles/resumes)

const mongoose = require('mongoose');

const experienceSchema = new mongoose.Schema({
    title: { type: String, required: true, trim: true },
    company: { type: String, required: true, trim: true },
    location: { type: String, trim: true },
    startDate: { type: Date, required: true },
    endDate: { type: Date }, // Null if current
    description: { type: String }
}, { _id: false });

const educationSchema = new mongoose.Schema({
    institution: { type: String, required: true, trim: true },
    degree: { type: String, required: true, trim: true },
    fieldOfStudy: { type: String, trim: true },
    startDate: { type: Date },
    endDate: { type: Date }, // Or graduation year
    grade: { type: String, trim: true }
}, { _id: false });

const profileSchema = new mongoose.Schema({
    candidateId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, unique: true },
    headline: { type: String, trim: true }, // e.g., "Full Stack Developer | MERN | AWS"
    summary: { type: String },
    skills: [{ type: String, trim: true, lowercase: true }],
    experience: [experienceSchema],
    education: [educationSchema],
    resumeUrl: { type: String, trim: true }, // URL to S3/Cloudinary stored resume PDF (optional)
    contact: {
        phone: { type: String, trim: true },
        linkedin: { type: String, trim: true },
        github: { type: String, trim: true },
        portfolio: { type: String, trim: true }
    },
    isVisible: { type: Boolean, default: true }, // For candidate to hide profile
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});

profileSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

profileSchema.index({ candidateId: 1 });
profileSchema.index({ skills: 1 }); // Crucial for recruiter search
profileSchema.index({ 'experience.title': 'text', 'experience.company': 'text', summary: 'text', skills: 'text' }); // Text index

module.exports = mongoose.model('Profile', profileSchema);
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

4. Application.js
(Based on the notebook, with updated status enum and added assignmentSubmissionId for the new module)

const mongoose = require('mongoose');

const applicationSchema = new mongoose.Schema({
    jobId: { type: mongoose.Schema.Types.ObjectId, ref: 'Job', required: true },
    candidateId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    employerId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, // Denormalized for quick employer filtering
    status: {
        type: String,
        enum: ['Applied', 'Viewed', 'Shortlisted', 'Assignment Sent', 'Assignment Submitted', 'Assignment Graded', 'Interviewing', 'Offered', 'Rejected', 'Withdrawn'],
        default: 'Applied'
    },
    applicationDate: { type: Date, default: Date.now },
    coverLetter: { type: String },
    // Snapshot of key profile info at time of application (optional, for historical record)
    profileSnapshot: {
        name: String,
        email: String,
        skills: [String],
        headline: String
    },
    assignmentSubmissionId: { type: mongoose.Schema.Types.ObjectId, ref: 'AssignmentSubmission', unique: true, sparse: true },
    updatedAt: { type: Date, default: Date.now }
});

applicationSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

applicationSchema.index({ jobId: 1, candidateId: 1 }, { unique: true }); // Candidate can apply once per job
applicationSchema.index({ jobId: 1 });
applicationSchema.index({ candidateId: 1 });
applicationSchema.index({ employerId: 1 });
applicationSchema.index({ status: 1 });
if (applicationSchema.paths.assignmentSubmissionId) { // Add index only if path exists
    applicationSchema.index({ assignmentSubmissionId: 1 }, { unique: true, sparse: true });
}


module.exports = mongoose.model('Application', applicationSchema);
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

5. Assignment.js (New Model)
(For the assignment posting module)

const mongoose = require('mongoose');

const assignmentSchema = new mongoose.Schema({
    jobId: { type: mongoose.Schema.Types.ObjectId, ref: 'Job', required: true },
    employerId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true }, // User who created/owns the assignment
    title: { type: String, required: true, trim: true },
    description: { type: String, required: true },
    instructions: { type: String }, // Detailed instructions for the candidate
    templateFileUrl: { type: String, trim: true }, // Optional: A file provided by employer (e.g., template, dataset)
    dueDate: { type: Date },
    timeToComplete: { type: Number }, // Optional: Estimated time in minutes/hours
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});

assignmentSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

assignmentSchema.index({ jobId: 1 });
assignmentSchema.index({ employerId: 1 });

module.exports = mongoose.model('Assignment', assignmentSchema);
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

6. AssignmentSubmission.js (New Model)
(For candidate submissions to assignments)
``
const mongoose = require('mongoose');

const assignmentSubmissionSchema = new mongoose.Schema({
    assignmentId: { type: mongoose.Schema.Types.ObjectId, ref: 'Assignment', required: true },
    applicationId: { type: mongoose.Schema.Types.ObjectId, ref: 'Application', required: true, unique: true }, // Each application can have one submission for a given assignment
    candidateId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    submissionDate: { type: Date, default: Date.now },
    submissionContent: { type: String }, // For text-based answers or links
    submissionFileUrl: { type: String, trim: true }, // For file uploads
    status: {
        type: String,
        enum: ['Pending', 'Submitted', 'Under Review', 'Reviewed', 'Passed', 'Failed'],
        default: 'Pending'
    },
    score: { type: Number }, // Optional score
    feedback: { type: String }, // Feedback from employer
    reviewedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, // Employer who reviewed
    reviewedDate: { type: Date },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
});

assignmentSubmissionSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

assignmentSubmissionSchema.index({ assignmentId: 1 });
assignmentSubmissionSchema.index({ applicationId: 1 }, { unique: true });
assignmentSubmissionSchema.index({ candidateId: 1 });
assignmentSubmissionSchema.index({ status: 1 });

module.exports = mongoose.model('AssignmentSubmission', assignmentSubmissionSchema);

``
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

Next Steps for Backend:

Create the directory job-portal/backend/models/ and save these files there.

Then, we can create:

job-portal/backend/config/db.js (for MongoDB connection logic)

job-portal/backend/config/index.js (for central config like JWT secret, port - though often done primarily with .env)

The main job-portal/backend/server.js file to set up Express.

Would you like me to provide the code for db.js and the initial server.js structure next? Once the backend foundation (models, DB connection, server setup) is in place, we can move to controllers and routes, and then tackle the frontend.




