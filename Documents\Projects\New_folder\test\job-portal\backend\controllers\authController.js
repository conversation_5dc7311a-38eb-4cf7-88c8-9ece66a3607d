const User = require('../models/User');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
// const crypto = require('crypto'); // For reset password token generation
// const sendEmail = require('../utils/sendEmail'); // For sending password reset emails

// Generate JWT
const generateToken = (id) => {
    return jwt.sign({ id }, process.env.JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRES_IN || '30d',
    });
};

// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Public
const registerUser = async (req, res, next) => {
    const { name, email, password, role, companyName } = req.body;

    try {
        // Check if user already exists
        let user = await User.findOne({ email });
        if (user) {
            return res.status(400).json({ message: 'User already exists' });
        }

        // Create new user
        user = new User({
            name,
            email,
            password, // Hashing is done by pre-save hook in User model
            role,
            companyName: role === 'employer' ? companyName : undefined,
        });

        await user.save();

        // Generate token
        const token = generateToken(user._id);

        res.status(201).json({
            success: true,
            token,
            user: {
                _id: user._id,
                name: user.name,
                email: user.email,
                role: user.role,
                companyName: user.companyName
            }
        });

    } catch (error) {
        console.error('Register error:', error.message);
        if (error.name === 'ValidationError') {
            const messages = Object.values(error.errors).map(val => val.message);
            return res.status(400).json({ message: messages.join(', ') });
        }
        res.status(500).json({ message: 'Server error during registration' });
    }
};

// @desc    Authenticate user & get token (Login)
// @route   POST /api/auth/login
// @access  Public
const loginUser = async (req, res, next) => {
    const { email, password } = req.body;

    try {
        // Check for user
        const user = await User.findOne({ email }).select('+password'); // Explicitly select password

        if (!user) {
            return res.status(401).json({ message: 'Invalid credentials - user not found' });
        }

        // Check if password matches
        const isMatch = await user.matchPassword(password);

        if (!isMatch) {
            return res.status(401).json({ message: 'Invalid credentials - password mismatch' });
        }

        // Generate token
        const token = generateToken(user._id);

        res.status(200).json({
            success: true,
            token,
            user: {
                _id: user._id,
                name: user.name,
                email: user.email,
                role: user.role,
                companyName: user.companyName
            }
        });

    } catch (error) {
        console.error('Login error:', error.message);
        res.status(500).json({ message: 'Server error during login' });
    }
};

// @desc    Get current logged-in user details
// @route   GET /api/auth/me
// @access  Private
const getMe = async (req, res, next) => {
    try {
        // req.user is set by the 'protect' middleware
        const user = await User.findById(req.user.id);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        res.status(200).json({
            success: true,
            data: {
                _id: user._id,
                name: user.name,
                email: user.email,
                role: user.role,
                companyName: user.companyName,
                createdAt: user.createdAt
            }
        });
    } catch (error) {
        console.error('GetMe error:', error.message);
        res.status(500).json({ message: 'Server error' });
    }
};

// @desc    Logout user (placeholder - JWT logout is mainly client-side)
// @route   POST /api/auth/logout
// @access  Private
const logoutUser = (req, res, next) => {
    // For JWT, logout is typically handled client-side by deleting the token.
    // Server-side, you might implement a token blocklist if needed for immediate invalidation.
    res.status(200).json({ success: true, message: 'User logged out successfully (client-side action required)' });
};

// @desc    Update user password
// @route   PUT /api/auth/updatepassword
// @access  Private
const updatePassword = async (req, res, next) => {
    const { currentPassword, newPassword } = req.body;

    try {
        const user = await User.findById(req.user.id).select('+password');

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Check if current password matches
        const isMatch = await user.matchPassword(currentPassword);
        if (!isMatch) {
            return res.status(401).json({ message: 'Incorrect current password' });
        }

        // Set new password (pre-save hook will hash it)
        user.password = newPassword;
        await user.save();

        // Optional: Generate a new token upon password change
        // const token = generateToken(user._id);

        res.status(200).json({ success: true, message: 'Password updated successfully' /*, token */ });

    } catch (error) {
        console.error('Update password error:', error.message);
        if (error.name === 'ValidationError') {
             const messages = Object.values(error.errors).map(val => val.message);
             return res.status(400).json({ message: messages.join(', ') });
        }
        res.status(500).json({ message: 'Server error updating password' });
    }
};

// @desc    Forgot password - Generate and send password reset token (simplified)
// @route   POST /api/auth/forgotpassword
// @access  Public
const forgotPassword = async (req, res, next) => {
    // This is a simplified version. A full implementation would:
    // 1. Find user by email.
    // 2. Generate a reset token (e.g., using crypto module), hash it, and save to user model with an expiry.
    // 3. Create a reset URL with the token.
    // 4. Send an email to the user with the reset URL (using a service like Nodemailer).
    res.status(501).json({ message: 'Forgot password functionality not fully implemented yet. This would typically send a reset email.' });
};

// @desc    Reset password using token (simplified)
// @route   PUT /api/auth/resetpassword/:resettoken
// @access  Public
const resetPassword = async (req, res, next) => {
    // This is a simplified version. A full implementation would:
    // 1. Get token from params, hash it, and find user by this hashed token & ensure it's not expired.
    // 2. If valid, update user's password with req.body.password.
    // 3. Clear the reset token fields on the user model.
    // 4. Save the user.
    res.status(501).json({ message: 'Reset password functionality not fully implemented yet. This would validate the token and update the password.' });
};

module.exports = {
    registerUser,
    loginUser,
    getMe,
    logoutUser,
    updatePassword,
    forgotPassword,
    resetPassword
};