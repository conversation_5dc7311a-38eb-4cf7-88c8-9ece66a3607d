{"version": 3, "sources": ["../../jwt-decode/lib/atob.js", "../../jwt-decode/lib/base64_url_decode.js", "../../jwt-decode/lib/index.js"], "sourcesContent": ["/**\n * The code was extracted from:\n * https://github.com/davidchambers/Base64.js\n */\n\nvar chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n\nfunction InvalidCharacterError(message) {\n    this.message = message;\n}\n\nInvalidCharacterError.prototype = new Error();\nInvalidCharacterError.prototype.name = \"InvalidCharacterError\";\n\nfunction polyfill(input) {\n    var str = String(input).replace(/=+$/, \"\");\n    if (str.length % 4 == 1) {\n        throw new InvalidCharacterError(\n            \"'atob' failed: The string to be decoded is not correctly encoded.\"\n        );\n    }\n    for (\n        // initialize result and counters\n        var bc = 0, bs, buffer, idx = 0, output = \"\";\n        // get next character\n        (buffer = str.charAt(idx++));\n        // character found in table? initialize bit storage and add its ascii value;\n        ~buffer &&\n        ((bs = bc % 4 ? bs * 64 + buffer : buffer),\n            // and if not first of each 4 characters,\n            // convert the first 8 bits to one ascii character\n            bc++ % 4) ?\n        (output += String.fromCharCode(255 & (bs >> ((-2 * bc) & 6)))) :\n        0\n    ) {\n        // try to find character in table (0-63, not found => -1)\n        buffer = chars.indexOf(buffer);\n    }\n    return output;\n}\n\nexport default (typeof window !== \"undefined\" &&\n    window.atob &&\n    window.atob.bind(window)) ||\npolyfill;", "import atob from \"./atob\";\n\nfunction b64DecodeUnicode(str) {\n    return decodeURIComponent(\n        atob(str).replace(/(.)/g, function(m, p) {\n            var code = p.charCodeAt(0).toString(16).toUpperCase();\n            if (code.length < 2) {\n                code = \"0\" + code;\n            }\n            return \"%\" + code;\n        })\n    );\n}\n\nexport default function(str) {\n    var output = str.replace(/-/g, \"+\").replace(/_/g, \"/\");\n    switch (output.length % 4) {\n        case 0:\n            break;\n        case 2:\n            output += \"==\";\n            break;\n        case 3:\n            output += \"=\";\n            break;\n        default:\n            throw \"Illegal base64url string!\";\n    }\n\n    try {\n        return b64DecodeUnicode(output);\n    } catch (err) {\n        return atob(output);\n    }\n}", "\"use strict\";\n\nimport base64_url_decode from \"./base64_url_decode\";\n\nexport function InvalidTokenError(message) {\n    this.message = message;\n}\n\nInvalidTokenError.prototype = new Error();\nInvalidTokenError.prototype.name = \"InvalidTokenError\";\n\nexport default function(token, options) {\n    if (typeof token !== \"string\") {\n        throw new InvalidTokenError(\"Invalid token specified\");\n    }\n\n    options = options || {};\n    var pos = options.header === true ? 0 : 1;\n    try {\n        return JSON.parse(base64_url_decode(token.split(\".\")[pos]));\n    } catch (e) {\n        throw new InvalidTokenError(\"Invalid token specified: \" + e.message);\n    }\n}"], "mappings": ";;;AAOA,SAASA,EAAsBC,IAAAA;AAC3BC,OAAKD,UAAUA;AAAAA;AAGnBD,EAAsBG,YAAY,IAAIC,SACtCJ,EAAsBG,UAAUE,OAAO;AA6BvC,IAAA,IAAkC,eAAA,OAAXC,UACnBA,OAAOC,QACPD,OAAOC,KAAKC,KAAKF,MAAAA,KA7BrB,SAAkBG,IAAAA;AACd,MAAIC,KAAMC,OAAOF,EAAAA,EAAOG,QAAQ,OAAO,EAAA;AACvC,MAAIF,GAAIG,SAAS,KAAK,EAClB,OAAM,IAAIb,EACN,mEAAA;AAGR,WAEgBc,IAAIC,IAAZC,IAAK,GAAeC,IAAM,GAAGC,IAAS,IAEzCH,KAASL,GAAIS,OAAOF,GAAAA,GAAAA,CAEpBF,OACCD,KAAKE,IAAK,IAAS,KAALF,KAAUC,KAASA,IAG/BC,MAAO,KACVE,KAAUP,OAAOS,aAAa,MAAON,OAAAA,KAAaE,IAAM,EAAA,IACzD,EAGAD,CAAAA,KA/BI,oEA+BWM,QAAQN,EAAAA;AAE3B,SAAOG;AAAAA;ACxBI,SAAA,EAASR,IAAAA;AACpB,MAAIQ,KAASR,GAAIE,QAAQ,MAAM,GAAA,EAAKA,QAAQ,MAAM,GAAA;AAClD,UAAQM,GAAOL,SAAS,GAAA;IACpB,KAAK;AACD;IACJ,KAAK;AACDK,MAAAA,MAAU;AACV;IACJ,KAAK;AACDA,MAAAA,MAAU;AACV;IACJ;AACI,YAAM;EAAA;AAGd,MAAA;AACI,WA5BR,SAA0BR,IAAAA;AACtB,aAAOY,mBACHf,EAAKG,EAAAA,EAAKE,QAAQ,QAAQ,SAASW,IAAGC,IAAAA;AAClC,YAAIC,KAAOD,GAAEE,WAAW,CAAA,EAAGC,SAAS,EAAA,EAAIC,YAAAA;AAIxC,eAHIH,GAAKZ,SAAS,MACdY,KAAO,MAAMA,KAEV,MAAMA;MAAAA,CAAAA,CAAAA;IAAAA,EAqBOP,EAAAA;EAAAA,SACnBW,IAAAA;AACL,WAAOtB,EAAKW,EAAAA;EAAAA;AAAAA;AC5Bb,SAASY,EAAkB7B,IAAAA;AAC9BC,OAAKD,UAAUA;AAAAA;AAMJ,SAAA,EAAS8B,IAAOC,IAAAA;AAC3B,MAAqB,YAAA,OAAVD,GACP,OAAM,IAAID,EAAkB,yBAAA;AAIhC,MAAIG,KAAAA,UADJD,KAAUA,MAAW,CAAA,GACHE,SAAkB,IAAI;AACxC,MAAA;AACI,WAAOC,KAAKC,MAAMC,EAAkBN,GAAMO,MAAM,GAAA,EAAKL,EAAAA,CAAAA,CAAAA;EAAAA,SAChDM,IAAAA;AACL,UAAM,IAAIT,EAAkB,8BAA8BS,GAAEtC,OAAAA;EAAAA;AAAAA;AAbpE6B,EAAkB3B,YAAY,IAAIC,SAClC0B,EAAkB3B,UAAUE,OAAO;AAAA,IAAA,yBAAA;", "names": ["InvalidCharacterError", "message", "this", "prototype", "Error", "name", "window", "atob", "bind", "input", "str", "String", "replace", "length", "bs", "buffer", "bc", "idx", "output", "char<PERSON>t", "fromCharCode", "indexOf", "decodeURIComponent", "m", "p", "code", "charCodeAt", "toString", "toUpperCase", "err", "InvalidTokenError", "token", "options", "pos", "header", "JSON", "parse", "base64_url_decode", "split", "e"]}