const Profile = require('../models/Profile');
const User = require('../models/User'); // To potentially delete user along with profile

// @desc    Get current logged-in candidate's profile
// @route   GET /api/profiles/me
// @access  Private/Candidate
const getCurrentCandidateProfile = async (req, res) => {
    try {
        const profile = await Profile.findOne({ candidateId: req.user._id }).populate('candidateId', 'name email');
        if (!profile) {
            return res.status(404).json({ message: 'Profile not found for this candidate.' });
        }
        res.json(profile);
    } catch (error) {
        console.error('Get current profile error:', error.message);
        res.status(500).json({ message: 'Server Error fetching profile.' });
    }
};

// @desc    Create or update current candidate's profile
// @route   POST /api/profiles
// @access  Private/Candidate
const createOrUpdateProfile = async (req, res) => {
    const {
        headline, summary, skills, experience, education, resumeUrl, contact, isVisible
    } = req.body;

    const profileFields = {};
    profileFields.candidateId = req.user._id;
    if (headline !== undefined) profileFields.headline = headline;
    if (summary !== undefined) profileFields.summary = summary;
    if (skills !== undefined) {
        profileFields.skills = Array.isArray(skills) ? skills.map(s => s.trim().toLowerCase()) : skills.split(',').map(s => s.trim().toLowerCase());
    }
    if (experience !== undefined) profileFields.experience = experience; // Expecting array of objects
    if (education !== undefined) profileFields.education = education; // Expecting array of objects
    if (resumeUrl !== undefined) profileFields.resumeUrl = resumeUrl;
    if (contact !== undefined) profileFields.contact = contact; // Expecting object
    if (isVisible !== undefined) profileFields.isVisible = isVisible;

    try {
        let profile = await Profile.findOne({ candidateId: req.user._id });

        if (profile) {
            // Update existing profile
            profile = await Profile.findOneAndUpdate(
                { candidateId: req.user._id },
                { $set: profileFields },
                { new: true, runValidators: true }
            ).populate('candidateId', 'name email');
            return res.json(profile);
        } else {
            // Create new profile
            profile = new Profile(profileFields);
            await profile.save();
            const populatedProfile = await Profile.findById(profile._id).populate('candidateId', 'name email');
            return res.status(201).json(populatedProfile);
        }
    } catch (error) {
        console.error('Create/Update profile error:', error.message);
        if (error.name === 'ValidationError') {
            const messages = Object.values(error.errors).map(val => val.message);
            return res.status(400).json({ message: "Validation Error", errors: messages });
        }
        res.status(500).json({ message: 'Server Error saving profile.' });
    }
};

// @desc    Get all candidate profiles (e.g., for employers to search)
// @route   GET /api/profiles
// @access  Private/Employer (or Admin)
const getAllProfiles = async (req, res) => {
    const pageSize = parseInt(req.query.pageSize) || 10;
    const page = parseInt(req.query.page) || 1;
    const sort = req.query.sort || '-updatedAt'; // Default sort by newest update

    let queryFilter = { isVisible: true }; // Only show visible profiles by default

    // Text search for headline, summary, skills (ensure text index exists)
    if (req.query.keyword) {
        queryFilter.$text = { $search: req.query.keyword };
    }
    // Skills filter
    if (req.query.skills) {
        const skillsArray = req.query.skills.split(',').map(skill => skill.trim().toLowerCase());
        queryFilter.skills = { $all: skillsArray };
    }
    // Location filter (if location is added to profile, e.g., profileFields.location)
    // if (req.query.location) { queryFilter.location = { $regex: req.query.location, $options: 'i' }; }

    try {
        const count = await Profile.countDocuments(queryFilter);
        const profiles = await Profile.find(queryFilter)
            .populate('candidateId', 'name email') // Populate basic user info
            .limit(pageSize)
            .skip(pageSize * (page - 1))
            .sort(sort);

        res.json({
            profiles,
            page,
            pages: Math.ceil(count / pageSize),
            count
        });
    } catch (error) {
        console.error('Get all profiles error:', error.message);
        res.status(500).json({ message: 'Server Error fetching profiles.' });
    }
};

// @desc    Get profile by user ID (e.g., for an employer to view a candidate's profile)
// @route   GET /api/profiles/user/:userId
// @access  Private (Employers or relevant users)
const getProfileByUserId = async (req, res) => {
    try {
        const profile = await Profile.findOne({ candidateId: req.params.userId, isVisible: true })
                                   .populate('candidateId', 'name email createdAt');
        if (!profile) {
            return res.status(404).json({ message: 'Profile not found or not visible.' });
        }
        res.json(profile);
    } catch (error) {
        console.error('Get profile by user ID error:', error.message);
        if (error.kind === 'ObjectId') {
            return res.status(404).json({ message: 'Profile not found (invalid user ID format).' });
        }
        res.status(500).json({ message: 'Server Error fetching profile.' });
    }
};

// @desc    Delete current candidate's profile (and optionally user account)
// @route   DELETE /api/profiles
// @access  Private/Candidate
const deleteProfile = async (req, res) => {
    try {
        // Find and remove the profile
        const profile = await Profile.findOneAndRemove({ candidateId: req.user._id });

        if (!profile) {
            return res.status(404).json({ message: 'Profile not found to delete.' });
        }

        // Optional: Remove the user account as well.
        // Be careful with this, ensure it's the desired behavior.
        // await User.findByIdAndRemove(req.user._id);

        res.json({ message: 'Profile deleted successfully.' });
    } catch (error) {
        console.error('Delete profile error:', error.message);
        res.status(500).json({ message: 'Server Error deleting profile.' });
    }
};

module.exports = {
    getCurrentCandidateProfile,
    createOrUpdateProfile,
    getAllProfiles,
    getProfileByUserId,
    deleteProfile
};