{"name": "ignore-by-default", "version": "1.0.1", "description": "A list of directories you should ignore by default", "main": "index.js", "files": ["index.js"], "scripts": {"test": "standard && node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/novemberborn/ignore-by-default.git"}, "keywords": ["ignore", "chokidar", "watcher", "exclude", "glob", "pattern"], "author": "<PERSON> (https://novemberborn.net/)", "license": "ISC", "bugs": {"url": "https://github.com/novemberborn/ignore-by-default/issues"}, "homepage": "https://github.com/novemberborn/ignore-by-default#readme", "devDependencies": {"figures": "^1.4.0", "standard": "^6.0.4"}}